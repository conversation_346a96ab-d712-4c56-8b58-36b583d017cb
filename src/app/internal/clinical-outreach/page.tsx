"use client";

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Phone, PhoneCall } from 'lucide-react';
import { toast } from 'sonner';

export default function ClinicalOutreachPage() {
  const [phoneNumber, setPhoneNumber] = useState('');
  const [isCallActive, setIsCallActive] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Phone number validation
  const validatePhoneNumber = (phone: string): boolean => {
    // Basic phone number validation - accepts various formats
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    const cleanPhone = phone.replace(/[\s\-\(\)\.]/g, '');
    return phoneRegex.test(cleanPhone) && cleanPhone.length >= 10;
  };

  const formatPhoneNumber = (phone: string): string => {
    // Clean and format phone number for E.164 format
    const cleanPhone = phone.replace(/[\s\-\(\)\.]/g, '');
    if (!cleanPhone.startsWith('+')) {
      return cleanPhone.startsWith('1') ? `+${cleanPhone}` : `+1${cleanPhone}`;
    }
    return cleanPhone;
  };

  const startOutboundCall = async () => {
    if (!phoneNumber.trim()) {
      toast.error("Please enter a phone number");
      return;
    }

    if (!validatePhoneNumber(phoneNumber)) {
      toast.error("Please enter a valid phone number");
      return;
    }

    setIsLoading(true);

    try {
      const formattedPhone = formatPhoneNumber(phoneNumber);

      // Call our backend API to initiate the outbound call
      toast.info("Initiating outbound call...");

      const response = await fetch('/api/outbound-call', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          phoneNumber: formattedPhone,
        }),
      });

      const result = await response.json();

      if (response.ok) {
        toast.success(`Clinical outreach call initiated to ${formattedPhone}`);
        setIsCallActive(true);

        // Reset form after successful call
        setTimeout(() => {
          setIsCallActive(false);
          setPhoneNumber('');
        }, 30000); // Reset after 30 seconds
      } else {
        throw new Error(result.error || 'Failed to initiate call');
      }
      
    } catch (error) {
      console.error("Failed to start outbound call:", error);
      toast.error("Failed to initiate call. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-background p-4">
      <div className="max-w-2xl mx-auto space-y-6">
        <div className="text-center space-y-2">
          <h1 className="text-3xl font-bold tracking-tight">Clinical Outreach</h1>
          <p className="text-muted-foreground">
            Post-procedure patient follow-up calls with AI assistance
          </p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Phone className="h-5 w-5" />
              Outbound Call Setup
            </CardTitle>
            <CardDescription>
              Enter a patient's phone number to initiate a post-procedure follow-up call with Liz, our AI assistant.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="phone">Patient Phone Number</Label>
              <Input
                id="phone"
                type="tel"
                placeholder="+****************"
                value={phoneNumber}
                onChange={(e) => setPhoneNumber(e.target.value)}
                className="text-lg"
              />
              <p className="text-sm text-muted-foreground">
                Enter phone number in any format (e.g., +**********, (*************, ************)
              </p>
            </div>

            <Button 
              onClick={startOutboundCall}
              disabled={isLoading || isCallActive}
              className="w-full"
              size="lg"
            >
              {isLoading ? (
                "Initiating Call..."
              ) : (
                <>
                  <PhoneCall className="mr-2 h-4 w-4" />
                  Start Clinical Outreach Call
                </>
              )}
            </Button>

            {isCallActive && (
              <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                <p className="text-green-800 font-medium">Call in progress...</p>
                <p className="text-green-600 text-sm">
                  Liz is conducting the clinical outreach call.
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>About Clinical Outreach</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <p className="text-sm text-muted-foreground">
              Our AI assistant Liz will conduct a professional follow-up call to:
            </p>
            <ul className="text-sm text-muted-foreground space-y-1 ml-4">
              <li>• Check on the patient's recovery progress</li>
              <li>• Ask about any concerns or symptoms</li>
              <li>• Provide support and reassurance</li>
              <li>• Collect feedback about their care experience</li>
              <li>• Schedule follow-up appointments if needed</li>
            </ul>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
