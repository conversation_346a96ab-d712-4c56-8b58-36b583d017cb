import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { phoneNumber } = await request.json();

    if (!phoneNumber) {
      return NextResponse.json(
        { error: 'Phone number is required' },
        { status: 400 }
      );
    }

    // Validate environment variables
    const vapiApiKey = process.env.VAPI_PRIVATE_API_KEY;
    const phoneNumberId = process.env.VAPI_PHONE_NUMBER_ID;

    if (!vapiApiKey) {
      console.error('VAPI_PRIVATE_API_KEY is not set');
      return NextResponse.json(
        { error: 'Vapi API key is not configured' },
        { status: 500 }
      );
    }

    if (!phoneNumberId) {
      console.error('VAPI_PHONE_NUMBER_ID is not set');
      return NextResponse.json(
        { error: 'Vapi phone number ID is not configured' },
        { status: 500 }
      );
    }

    // Configure the assistant for clinical outreach
    const assistantConfig = {
      transcriber: {
        provider: "deepgram",
        model: "nova-2",
        language: "en-US",
      },
      credentials: [
        {
          provider: "11labs",
          apiKey: "sk_004c5ec28fe947fe30210fdd08b974d720b6ba2e34b113e0"
        }
      ],
      backgroundSound: "office",
      model: {
        provider: "openai",
        model: "gpt-4o-mini",
        messages: [
          {
            role: "system",
            content: `You are a helpful assistant named Liz. You are conducting post-procedure clinical outreach for a healthcare organization. 

[Identity]
You are Liz, a caring and professional healthcare assistant conducting follow-up calls for patients who have recently undergone medical procedures.

[Style]
- Maintain a warm, empathetic, and professional tone
- Show genuine concern for the patient's wellbeing
- Use clear, simple language that patients can easily understand
- Be patient and allow time for responses
- Keep responses concise and to the point for voice calls

[Goals]
1. Check on the patient's recovery and overall wellbeing
2. Ask about any concerns, symptoms, or complications
3. Provide reassurance and support
4. Collect feedback about their care experience
5. Schedule follow-up appointments if needed
6. Answer any questions they may have

[Guidelines]
- Always introduce yourself and explain the purpose of the call
- Ask open-ended questions about their recovery
- Listen actively and respond with empathy
- Provide clear next steps or recommendations
- Ensure they know how to reach healthcare providers if needed
- Keep the call focused but allow for patient concerns
- If the patient has serious concerns, advise them to contact their healthcare provider immediately

[Sample Opening]
"Hello, this is Liz calling from your healthcare team. I'm reaching out to check on how you're doing after your recent procedure. Do you have a few minutes to talk about your recovery?"`,
          },
        ],
      },
      voice: {
        provider: "11labs",
        voiceId: "M6N6IdXhi5YNZyZSDe7k",
      },
      name: "Clinical Outreach Assistant - Liz",
      firstMessage: "Hello, this is Liz calling from your healthcare team. I'm reaching out to check on how you're doing after your recent procedure. Do you have a few minutes to talk about your recovery?",
      firstMessageMode: "assistant-speaks-first",
    };

    // Make the outbound call using Vapi API
    const response = await fetch('https://api.vapi.ai/call', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${vapiApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        assistant: assistantConfig,
        phoneNumberId: phoneNumberId,
        customer: {
          number: phoneNumber,
        },
      }),
    });

    if (!response.ok) {
      const errorData = await response.text();
      console.error('Vapi API error:', response.status, errorData);
      return NextResponse.json(
        { error: 'Failed to initiate call', details: errorData },
        { status: response.status }
      );
    }

    const callData = await response.json();
    
    return NextResponse.json({
      success: true,
      callId: callData.id,
      message: 'Outbound call initiated successfully',
    });

  } catch (error) {
    console.error('Error initiating outbound call:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
